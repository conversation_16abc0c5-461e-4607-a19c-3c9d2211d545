import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { FileText, Info, Trash2, BarChart3, CheckSquare, Square, MessageSquare, X, AlertTriangle } from 'lucide-react';
import { ResponsiveContainer, AreaChart, Area, CartesianGrid, XAxis, YAxis, Tooltip, Legend } from 'recharts';
import Card from '../common/Card';
import Button from '../common/Button';
import { useDashboard } from '../../contexts/DashboardContext';
import type { Scenario as BaseScenario } from '../../types';
import {
  initializeDisclosureData,
  type DisclosureItem
} from '../../services/disclosureService';
import {
  prepareChartData,
  calculateKeyMetrics,
  type ScenarioTableData
} from '../../services/scenarioService';
import {
  generateAsIsLoanTableData,
  generateFaceAmountTableData,
  generateFaceAmountVariesByYearTableData,
  generatePremiumAmountTableData,
  generateStopPremiumAmountTableData,
  AsIsLoan,
  FaceAmountVariesByYear,
  FaceAmount,
  StopPremiumAmount,
  PremiumAmount
} from '../../data/mockScenarioData';
import { formatCurrency, formatCurrencyForTable, formatPercentage } from '../../utils/currencyFormatter';
import {
  fetchAICommentary,
  processCommentary,
  formatScenarioDataForLLM,
  type ProcessedCommentary
} from '../../services/commentaryService';

type ScenarioWithKeyPoints = BaseScenario & { keyPoints?: string[] };

// Utility function for ordinal numbers (1st, 2nd, 3rd, etc.)

const formatOrdinal = (n: number): string => {
  if (!Number.isInteger(n) || n <= 0) {
    return 'Unknown';
  }
  
  const s = ['th', 'st', 'nd', 'rd'];
  const v = n % 100;
  return n + (s[(v - 20) % 10] || s[v] || s[0]);
};

// Function to get detailed table configuration and data based on scenario type
const getDetailedTableConfig = (scenario: any) => {
  console.log('📊 Getting detailed table config for scenario:', scenario);

  // Determine table type based on scenario name and category
  const scenarioName = scenario.name?.toLowerCase() || '';
  const scenarioCategory = scenario.category || '';

  // As-Is scenarios - use AsIsLoanTableData
  if (scenarioCategory === 'as-is' || scenarioName.includes('as-is')) {
    return {
      columns: AsIsLoan,
      data: generateAsIsLoanTableData(),
      title: 'As-Is Cash Value Analysis'
    };
  }

  // Face Amount change to new amount - use FaceAmountTableData
  if (scenarioCategory === 'face-amount' || scenarioName.includes('face amount')) {
    // Check if it's modify by year scenario - use FaceAmountVariesByYearTableData
    if (scenarioName.includes('modify') || scenarioName.includes('by year') || scenarioName.includes('varies')) {
      return {
        columns: FaceAmountVariesByYear,
        data: generateFaceAmountVariesByYearTableData(),
        title: 'Face Amount Varies By Year Analysis'
      };
    } else {
      return {
        columns: FaceAmount,
        data: generateFaceAmountTableData(),
        title: 'Face Amount Analysis'
      };
    }
  }

  // Premium scenarios
  if (scenarioCategory === 'premium' || scenarioName.includes('premium')) {
    // Check if it's stop premium scenario - use StopPremiumAmountTableData
    if (scenarioName.includes('stop') || scenarioName.includes('cease') || scenarioName.includes('future premium')) {
      return {
        columns: StopPremiumAmount,
        data: generateStopPremiumAmountTableData(),
        title: 'Stop Premium Analysis'
      };
    } else {
      // Premium enter new premium amount - use PremiumAmountTableData
      return {
        columns: PremiumAmount,
        data: generatePremiumAmountTableData(),
        title: 'Premium Amount Analysis'
      };
    }
  }

  // Default to As-Is table
  return {
    columns: AsIsLoan,
    data: generateAsIsLoanTableData(),
    title: 'Default Analysis'
  };
};

// Function to get hardcoded table data based on scenario
const getTableDataForScenario = async (scenario: any): Promise<ScenarioTableData[]> => {
  console.log('📊 Getting table data for scenario:', scenario);

  // Check if this is a scenario loaded from backend (has backend data)
  if (scenario.data?.backendData) {
    console.log('🔍 Scenario loaded from backend, checking for real data availability');

    // For now, we'll use hardcoded data but this is where we would
    // fetch real illustration table data from the backend in the future
    // TODO: Implement backend table data fetching when available
    console.log('📊 Backend scenario detected, but using mock data for now (real table data endpoint not yet implemented)');
  }

  // Use hardcoded data (for both new scenarios and backend scenarios until table data endpoint is ready)
  console.log('📊 Using hardcoded mock data for scenario');
  return getHardcodedTableData(scenario);
};

const getHardcodedTableData = (scenario: any): ScenarioTableData[] => {
  console.log('📊 Getting hardcoded table data for scenario:', scenario);

  // Map scenario categories to appropriate data functions
  const scenarioId = parseInt(scenario.id);

  let rawData: any[] = [];

  // Determine which hardcoded data to use based on scenario category or ID
  if (scenario.category === 'as-is' || scenario.category === 'as-is-saved' || scenarioId === 1) {
    rawData = generateAsIsLoanTableData();
  } else if (scenario.category === 'premium' || scenarioId === 2) {
    rawData = generatePremiumAmountTableData();
  } else if (scenario.category === 'face-amount' || scenarioId === 3) {
    rawData = generateFaceAmountTableData();
  } else if (scenario.category === 'policy-performance' || scenarioId === 4) {
    rawData = generateFaceAmountVariesByYearTableData();
  } else if (scenario.category === 'risk-assessment' || scenarioId === 5) {
    rawData = generateStopPremiumAmountTableData();
  } else {
    // Default to As-Is data
    rawData = generateAsIsLoanTableData();
  }

  // Convert the detailed hardcoded data to the simplified ScenarioTableData format
  const convertedData: ScenarioTableData[] = rawData.map((row, index) => ({
    policyYear: row.policyYear || (index + 1),
    endOfAge: row.age || (40 + index),
    plannedPremium: row.plannedPremium || 2500,
    netOutlay: (row.plannedPremium || 2500) * (index + 1), // Cumulative calculation
    netSurrenderValue: row.netCashValue || row.netValueBeginningOfYear || (1000 * (index + 1)),
    netDeathBenefit: row.faceAmount || 350000 // Use face amount or default
  }));

  console.log('✅ Converted hardcoded data:', {
    scenarioCategory: scenario.category,
    scenarioName: scenario.name,
    originalRows: rawData.length,
    convertedRows: convertedData.length,
    firstRow: convertedData[0],
    lastRow: convertedData[convertedData.length - 1],
    sampleData: convertedData.slice(0, 3) // Show first 3 rows for debugging
  });

  return convertedData;
};

// Memoized scenario card component to prevent unnecessary re-renders
const ScenarioCard = React.memo(({
  scenario,
  isSelected,
  onScenarioClick,
  onDeleteScenario
}: {
  scenario: ScenarioWithKeyPoints;
  isSelected: boolean;
  onScenarioClick: (id: string) => void;
  onDeleteScenario: (id: string, event: React.MouseEvent) => void;
}) => (
  <div
    key={scenario.id}
    onClick={() => onScenarioClick(scenario.id)}
    className={`
      bg-white rounded-lg border p-5 cursor-pointer transition-all duration-200 relative
      ${isSelected
        ? 'border-blue-500 bg-blue-50 shadow-lg ring-2 ring-blue-200'
        : 'border-gray-200 hover:border-blue-300 hover:shadow-sm'
      }
    `}
  >
    {/* Delete Button */}
    <button
      onClick={(e) => onDeleteScenario(scenario.id, e)}
      className="absolute top-3 right-3 p-1.5 text-gray-400 hover:text-red-500 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg transition-colors"
      title="Delete scenario"
    >
      <Trash2 className="w-4 h-4" />
    </button>

    {/* Scenario Content */}
    <div className="pr-8">
      <h3 className="font-semibold text-gray-900 mb-2 text-lg">{scenario.name}</h3>

      {/* As-Is Details */}
      {scenario.asIsDetails && (
        <div className="mb-3">
          <p className="text-sm text-gray-600 font-medium mb-1">Question:</p>
          <p className="text-sm text-gray-700">{scenario.asIsDetails}</p>
        </div>
      )}

      {/* What-If Options */}
      {scenario.whatIfOptions && scenario.whatIfOptions.length > 0 && (
        <div className="mb-3">
          <p className="text-sm text-gray-600 font-medium mb-1">Selected Options:</p>
          <ul className="text-sm text-gray-700 space-y-1">
            {scenario.whatIfOptions.map((option, index) => (
              <li key={index} className="flex items-start">
                <span className="text-blue-500 mr-2">•</span>
                <span>{option}</span>
              </li>
            ))}
          </ul>
        </div>
      )}

      {/* Key Points */}
      {scenario.keyPoints && scenario.keyPoints.length > 0 && (
        <div className="mb-3">
          <p className="text-sm text-gray-600 font-medium mb-1">Key Points:</p>
          <ul className="text-sm text-gray-700 space-y-1">
            {scenario.keyPoints.filter(Boolean).slice(0, 3).map((point, index) => (
              <li key={index} className="flex items-start">
                <span className="text-green-500 mr-2">•</span>
                <span>{point}</span>
              </li>
            ))}
          </ul>
        </div>
      )}


    </div>
  </div>
));

const SelectedScenarios: React.FC = () => {
  const { scenarios, selectedCustomerData, selectedPolicyData, setActiveTab, deleteScenario, addScenario, loadScenariosFromBackend } = useDashboard();
  const [selectedScenario, setSelectedScenario] = useState<string | null>(null);
  const [showComparisonSelected, setShowComparisonSelected] = useState(false);
  const [showAiCommentaryPopup, setShowAiCommentaryPopup] = useState(false);
  const [aiCommentary, setAiCommentary] = useState<ProcessedCommentary | null>(null);
  const [isLoadingCommentary, setIsLoadingCommentary] = useState(false);
  const [commentaryError, setCommentaryError] = useState<string | null>(null);

  const [showDisclosurePopup, setShowDisclosurePopup] = useState(false);

  // Disclosure data state (only disclosure uses backend)
  const [disclosureData, setDisclosureData] = useState<DisclosureItem[]>([]);
  const [loadingDisclosure, setLoadingDisclosure] = useState(false);
  const [disclosureError, setDisclosureError] = useState<string | null>(null);

  // Table data state for selected scenario (using hardcoded data)
  const [scenarioTableData, setScenarioTableData] = useState<ScenarioTableData[]>([]);
  const [isLoadingChart, setIsLoadingChart] = useState(false);

  // Detailed table data state for full table display
  const [detailedTableData, setDetailedTableData] = useState<any[]>([]);
  const [tableColumns, setTableColumns] = useState<any[]>([]);

  // Modern chart colors with gradients
  const chartColors = useMemo(() => ({
    plannedPremium: '#6366F1', // Modern indigo
    netOutlay: '#10B981',      // Emerald green
    netSurrenderValue: '#8B5CF6', // Purple
    netDeathBenefit: '#F59E0B'    // Amber
  }), []);



  // Calculate policy year with enhanced date parsing (optimized)
  const calculatePolicyYear = React.useMemo((): string => {
    // Try to find issue date from multiple possible fields
    const possibleDateFields = [
      selectedPolicyData?.issueDate,
      selectedPolicyData?.policyStartDate
    ];

    let issueDate = null;

    // Find the first valid date
    for (const dateField of possibleDateFields) {
      if (dateField) {
        issueDate = dateField;
        break;
      }
    }

    if (!issueDate) {
      return 'Unknown';
    }

    // Parse the date
    let parsedIssueDate: Date;
    try {
      parsedIssueDate = new Date(issueDate);
      if (isNaN(parsedIssueDate.getTime())) {
        throw new Error('Invalid date');
      }
    } catch (error) {
      return 'Unknown';
    }

    // Calculate years difference
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    let yearDiff = today.getFullYear() - parsedIssueDate.getFullYear();

    // Check if we've passed the anniversary this year
    const anniversaryThisYear = new Date(parsedIssueDate);
    anniversaryThisYear.setFullYear(today.getFullYear());
    anniversaryThisYear.setHours(0, 0, 0, 0);

    // If today is before this year's anniversary, we're still in the previous policy year
    if (today < anniversaryThisYear) {
      yearDiff--;
    }

    const currentPolicyYear = yearDiff + 1;

    // Validate the result
    if (!Number.isInteger(currentPolicyYear) || currentPolicyYear < 1 || currentPolicyYear > 100) {
      return 'Unknown';
    }

    return `${formatOrdinal(currentPolicyYear)}`;
  }, [selectedPolicyData?.issueDate, selectedPolicyData?.policyStartDate]);

  // Memoize scenarios display to prevent unnecessary re-renders
  const displayScenarios: ScenarioWithKeyPoints[] = useMemo(() => {
    return (scenarios as ScenarioWithKeyPoints[]).filter(() => {
      // Show all scenarios that were saved in the current session
      return true; // Show all current session scenarios
    });
  }, [scenarios]);

  const handleScenarioClick = useCallback((scenarioId: string) => {
    setSelectedScenario(selectedScenario === scenarioId ? null : scenarioId);
  }, [selectedScenario]);

  const handleDeleteScenario = useCallback(async (scenarioId: string, event: React.MouseEvent) => {
    event.stopPropagation(); // Prevent triggering the card click

    if (window.confirm('Are you sure you want to delete this scenario? This action cannot be undone.')) {
      try {
        await deleteScenario(scenarioId);
        // If the deleted scenario was currently selected, clear the selection
        if (selectedScenario === scenarioId) {
          setSelectedScenario(null);
        }
      } catch (error) {
        console.error('Failed to delete scenario:', error);
        alert('Failed to delete scenario. Please try again.');
      }
    }
  }, [selectedScenario, deleteScenario]);

  // Get selected scenario data
  const selectedScenarioData = selectedScenario ?
    displayScenarios.find(s => s.id === selectedScenario) : null;

  // Handle AI Commentary generation
  const handleAICommentary = useCallback(async () => {
    if (!selectedScenarioData) {
      console.error('❌ No scenario data available for AI commentary');
      return;
    }

    setIsLoadingCommentary(true);
    setCommentaryError(null);

    try {
      console.log('🤖 Generating AI commentary for scenario:', selectedScenarioData.name);

      // Get the table data for the selected scenario
      const tableConfig = getDetailedTableConfig(selectedScenarioData);
      const tableData = tableConfig?.data || [];

      // Format the data for LLM service
      const formattedData = formatScenarioDataForLLM(tableData);

      // Prepare request with additional context
      const requestData = {
        policyInfo: {
          policyNumber: selectedCustomerData?.policyNumber || selectedCustomerData?.details?.["Policy Number"],
          customerName: selectedCustomerData?.name,
          policyType: selectedCustomerData?.details?.["Policy Type"] || selectedPolicyData?.name,
          faceAmount: selectedPolicyData?.faceAmount?.toString() || selectedPolicyData?.coverage,
          annualPremium: selectedPolicyData?.premiumAmount?.toString() || selectedPolicyData?.premium,
          currentAge: selectedCustomerData?.details?.DOB ?
            (new Date().getFullYear() - new Date(selectedCustomerData.details.DOB).getFullYear()).toString() :
            undefined,
          retirementAge: '65' // Default retirement age
        },
        scenarioData: formattedData,
        scenarioName: selectedScenarioData.name,
        scenarioCategory: selectedScenarioData.category,
        additionalContext: `This is a ${selectedScenarioData.category} scenario analysis for policy evaluation.`
      };

      // Call the AI commentary service
      const response = await fetchAICommentary(requestData);

      // Validate response
      if (!response || !response.response || !response.response.formatted) {
        throw new Error('Invalid response from AI Commentary service');
      }

      // Process the response into structured sections
      const processedCommentary = processCommentary(response.response.formatted);

      // Validate processed commentary has actual content
      if (!processedCommentary.summary &&
          processedCommentary.recommendations.length === 0 &&
          processedCommentary.risks.length === 0 &&
          processedCommentary.keyInsights.length === 0) {
        throw new Error('No valid commentary content received from backend');
      }

      setAiCommentary(processedCommentary);
      console.log('✅ AI commentary generated successfully');

    } catch (error) {
      console.error('❌ Error generating AI commentary:', error);

      // Provide specific error messages based on error type
      let errorMessage = 'Failed to generate AI commentary.';

      if (error instanceof Error) {
        if (error.message.includes('Cannot connect') || error.message.includes('fetch')) {
          errorMessage = 'Cannot connect to AI Commentary service. Please ensure the backend service is running.';
        } else if (error.message.includes('Invalid response') || error.message.includes('No valid commentary')) {
          errorMessage = 'AI Commentary service is not providing valid responses. Please check the service configuration.';
        } else if (error.message.includes('No scenario data')) {
          errorMessage = 'No scenario data available for AI commentary generation.';
        } else {
          errorMessage = error.message;
        }
      }

      setCommentaryError(errorMessage);
    } finally {
      setIsLoadingCommentary(false);
    }
  }, [selectedScenarioData, selectedPolicyData, selectedCustomerData]);

  // Fetch disclosure data and scenarios when component mounts or policy data changes
  useEffect(() => {
    const fetchDataForPolicy = async () => {
      if (!selectedPolicyData?.id) {
        return;
      }

      // Load disclosure data
      await initializeDisclosureData(
        selectedCustomerData,
        selectedPolicyData,
        setLoadingDisclosure,
        setDisclosureError,
        setDisclosureData
      );

      // Load scenarios from backend
      try {
        const policyId = parseInt(selectedPolicyData.id);
        await loadScenariosFromBackend(policyId);
      } catch (error) {
        console.error('❌ Error loading scenarios:', error);
      }
    };

    fetchDataForPolicy();
  }, [selectedCustomerData?.customerId, selectedPolicyData?.id]);

  // Load table data when scenario is selected (using hardcoded data)
  useEffect(() => {
    const loadTableData = async () => {
      if (!selectedScenario || !selectedPolicyData) {
        setScenarioTableData([]);
        setIsLoadingChart(false);
        return;
      }

      const scenario = displayScenarios.find(s => s.id === selectedScenario);
      if (!scenario) {
        console.warn('⚠️ Selected scenario not found in displayScenarios');
        setScenarioTableData([]);
        setIsLoadingChart(false);
        return;
      }

      try {
        // Create guaranteed working data for chart demonstration
        const guaranteedTableData: ScenarioTableData[] = Array.from({ length: 25 }, (_, i) => ({
          policyYear: i + 1,
          endOfAge: 40 + i,
          plannedPremium: 2500,
          netOutlay: 2500 * (i + 1), // Cumulative
          netSurrenderValue: Math.max(0, (i + 1) * 1500 - 500), // Growing cash value
          netDeathBenefit: 350000 + (i * 1000) // Slightly growing death benefit
        }));

        // Try to get the proper data, but fall back to guaranteed data
        let tableData: ScenarioTableData[] = guaranteedTableData;
        try {
          const generatedData = await getTableDataForScenario(scenario);
          if (generatedData && generatedData.length > 0) {
            tableData = generatedData;
          }
        } catch (error) {
          console.error('❌ Error in getTableDataForScenario, using guaranteed data:', error);
        }

        // Get detailed table configuration and data
        let detailedConfig;
        try {
          detailedConfig = getDetailedTableConfig(scenario);
        } catch (error) {
          console.error('❌ Error in getDetailedTableConfig:', error);
          detailedConfig = { data: [], columns: [], title: 'Chart Analysis' };
        }

        // Set the data (guaranteed to have content)
        setScenarioTableData(tableData);
        setDetailedTableData(detailedConfig.data);
        setTableColumns(detailedConfig.columns);

        // Clear loading state immediately for static data
        setIsLoadingChart(false);
      } catch (error) {
        console.error('❌ Error in data loading process:', error);
        // Create guaranteed fallback data
        const emergencyFallbackData: ScenarioTableData[] = Array.from({ length: 20 }, (_, i) => ({
          policyYear: i + 1,
          endOfAge: 40 + i,
          plannedPremium: 2500,
          netOutlay: 2500 * (i + 1),
          netSurrenderValue: 1000 * (i + 1),
          netDeathBenefit: 350000
        }));
        setScenarioTableData(emergencyFallbackData);
        setDetailedTableData([]);
        setTableColumns([]);
        setIsLoadingChart(false);
      }
    };

    loadTableData().catch(error => {
      console.error('❌ Error in loadTableData:', error);
      // Set fallback data on error
      setScenarioTableData([]);
      setIsLoadingChart(false);
    });
  }, [selectedScenario, scenarios, selectedPolicyData?.id]);

  // Memoized chart data preparation to prevent flickering
  const chartData = useMemo(() => {
    console.log('📊 Chart data preparation - scenarioTableData:', scenarioTableData);
    if (scenarioTableData.length === 0) {
      console.log('📊 Chart data empty - no scenario table data available');
      return [];
    }
    const data = prepareChartData(scenarioTableData);
    console.log('📊 Chart data prepared successfully:', {
      inputRows: scenarioTableData.length,
      outputRows: data.length,
      firstDataPoint: data[0],
      lastDataPoint: data[data.length - 1]
    });
    return data;
  }, [scenarioTableData]);

  // Custom tooltip formatter
  const formatTooltip = useCallback((value: any, name: string) => {
    return [formatCurrency(value), name];
  }, []);

  // Memoized area chart rendering function to prevent flickering
  const renderAreaChart = useCallback(() => {
    // Show loading only if explicitly loading and no data yet
    if (isLoadingChart && (!chartData || chartData.length === 0)) {
      return (
        <div className="flex items-center justify-center h-[400px] bg-gray-50 rounded-lg">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto mb-3"></div>
            <p className="text-gray-500">Loading chart data...</p>
          </div>
        </div>
      );
    }

    if (!chartData || chartData.length === 0) {
      return (
        <div className="flex items-center justify-center h-[400px] bg-gray-50 dark:bg-gray-800 rounded-lg">
          <div className="text-center">
            <BarChart3 className="w-12 h-12 text-gray-400 mx-auto mb-3" />
            <p className="text-gray-500 dark:text-gray-400">No chart data available</p>
            <p className="text-xs text-gray-400 mt-2">Scenario table data: {scenarioTableData.length} rows</p>
            <p className="text-xs text-gray-400">Chart data: {chartData?.length || 0} points</p>
          </div>
        </div>
      );
    }

    return (
      <ResponsiveContainer width="100%" height={400}>
        <AreaChart
          data={chartData}
          margin={{ top: 30, right: 40, left: 20, bottom: 20 }}
        >
          {/* Gradient Definitions */}
          <defs>
            <linearGradient id="plannedPremiumGradient" x1="0" y1="0" x2="0" y2="1">
              <stop offset="5%" stopColor={chartColors.plannedPremium} stopOpacity={0.8}/>
              <stop offset="95%" stopColor={chartColors.plannedPremium} stopOpacity={0.1}/>
            </linearGradient>
            <linearGradient id="netOutlayGradient" x1="0" y1="0" x2="0" y2="1">
              <stop offset="5%" stopColor={chartColors.netOutlay} stopOpacity={0.8}/>
              <stop offset="95%" stopColor={chartColors.netOutlay} stopOpacity={0.1}/>
            </linearGradient>
            <linearGradient id="netSurrenderValueGradient" x1="0" y1="0" x2="0" y2="1">
              <stop offset="5%" stopColor={chartColors.netSurrenderValue} stopOpacity={0.8}/>
              <stop offset="95%" stopColor={chartColors.netSurrenderValue} stopOpacity={0.1}/>
            </linearGradient>
            <linearGradient id="netDeathBenefitGradient" x1="0" y1="0" x2="0" y2="1">
              <stop offset="5%" stopColor={chartColors.netDeathBenefit} stopOpacity={0.8}/>
              <stop offset="95%" stopColor={chartColors.netDeathBenefit} stopOpacity={0.1}/>
            </linearGradient>
          </defs>

          {/* Modern Grid */}
          <CartesianGrid
            strokeDasharray="2 4"
            stroke="#e5e7eb"
            strokeOpacity={0.3}
            vertical={false}
          />

          {/* X-Axis */}
          <XAxis
            dataKey="year"
            axisLine={false}
            tickLine={false}
            tick={{
              fontSize: 12,
              fill: '#6b7280',
              fontWeight: 500
            }}
            tickMargin={10}
          />

          {/* Y-Axis */}
          <YAxis
            axisLine={false}
            tickLine={false}
            tick={{
              fontSize: 12,
              fill: '#6b7280',
              fontWeight: 500
            }}
            tickFormatter={(value) => `$${(value / 1000).toFixed(0)}K`}
            tickMargin={10}
          />

          {/* Modern Tooltip */}
          <Tooltip
            formatter={formatTooltip}
            labelFormatter={(label) => `Policy Year: ${label}`}
            contentStyle={{
              backgroundColor: 'rgba(255, 255, 255, 0.98)',
              border: 'none',
              borderRadius: '12px',
              boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
              padding: '16px',
              fontSize: '14px',
              fontWeight: '500'
            }}
            labelStyle={{
              color: '#374151',
              fontWeight: '600',
              marginBottom: '8px'
            }}
          />

          {/* Modern Legend */}
          <Legend
            wrapperStyle={{
              paddingTop: '20px',
              fontSize: '14px',
              fontWeight: '500'
            }}
          />

          {/* Areas - Non-stacked for clearer visualization */}
          <Area
            type="monotone"
            dataKey="Net Death Benefit"
            stroke={chartColors.netDeathBenefit}
            fill="url(#netDeathBenefitGradient)"
            strokeWidth={3}
            dot={false}
            activeDot={{
              r: 6,
              stroke: chartColors.netDeathBenefit,
              strokeWidth: 2,
              fill: '#fff'
            }}
          />
          <Area
            type="monotone"
            dataKey="Net Surrender Value"
            stroke={chartColors.netSurrenderValue}
            fill="url(#netSurrenderValueGradient)"
            strokeWidth={3}
            dot={false}
            activeDot={{
              r: 6,
              stroke: chartColors.netSurrenderValue,
              strokeWidth: 2,
              fill: '#fff'
            }}
          />
          <Area
            type="monotone"
            dataKey="Net Outlay"
            stroke={chartColors.netOutlay}
            fill="url(#netOutlayGradient)"
            strokeWidth={3}
            dot={false}
            activeDot={{
              r: 6,
              stroke: chartColors.netOutlay,
              strokeWidth: 2,
              fill: '#fff'
            }}
          />
          <Area
            type="monotone"
            dataKey="Planned Premium"
            stroke={chartColors.plannedPremium}
            fill="url(#plannedPremiumGradient)"
            strokeWidth={3}
            dot={false}
            activeDot={{
              r: 6,
              stroke: chartColors.plannedPremium,
              strokeWidth: 2,
              fill: '#fff'
            }}
          />
        </AreaChart>
      </ResponsiveContainer>
    );
  }, [chartData, chartColors, formatTooltip, isLoadingChart]);

  return (
    <div className="space-y-6 px-6">
      {/* Current Policy Information Card - Only Show After Policy Selection */}
      {selectedPolicyData && (
        <Card className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
          <div className="flex items-center space-x-3 mb-4">
            <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900/40 rounded-lg flex items-center justify-center">
              <BarChart3 className="w-5 h-5 text-blue-600 dark:text-blue-400" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Current Policy Information</h3>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* First Row */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-gray-500 dark:text-gray-400 font-medium">Policy Number</p>
                <p className="text-base font-bold text-gray-900 dark:text-white">
                  {selectedCustomerData?.policyNumber || selectedPolicyData?.id || '1'}
                </p>
              </div>
              <div>
                <p className="text-sm text-gray-500 dark:text-gray-400 font-medium">Customer Name</p>
                <p className="text-base font-bold text-gray-900 dark:text-white">
                  {selectedCustomerData?.name || selectedPolicyData?.name || 'John Michael Smith'}
                </p>
              </div>
              <div>
                <p className="text-sm text-gray-500 dark:text-gray-400 font-medium">Customer ID</p>
                <p className="text-base font-bold text-gray-900 dark:text-white">
                  {selectedCustomerData?.customerId || selectedPolicyData?.id || '1'}
                </p>
              </div>
              <div>
                <p className="text-sm text-gray-500 dark:text-gray-400 font-medium">Date of Birth</p>
                <p className="text-base font-bold text-gray-900 dark:text-white">
                  {selectedCustomerData?.details?.DOB || '03-15-1985'}
                </p>
              </div>
            </div>
            
            {/* Second Row */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-gray-500 dark:text-gray-400 font-medium">Policy Type</p>
                <p className="text-base font-bold text-gray-900 dark:text-white">
                  {selectedPolicyData?.name || 'Whole Life Insurance'}
                </p>
                <div className="mt-2">
                  <p className="text-sm text-gray-500 dark:text-gray-400 font-medium">Annual Premium</p>
                  <p className="text-base font-bold text-gray-900 dark:text-white">
                    $ {selectedPolicyData?.premium ?
                      parseInt(selectedPolicyData.premium.replace(/[,$]/g, '').replace(/annually/i, '').trim().match(/(\d+)/)?.[1] || '0').toLocaleString() :
                      '2,500'}
                  </p>
                </div>
              </div>
              <div>
                <p className="text-sm text-gray-500 dark:text-gray-400 font-medium">Face Amount</p>
                <p className="text-base font-bold text-gray-900 dark:text-white">
                  $ {selectedPolicyData?.coverage ?
                    (typeof selectedPolicyData.coverage === 'string' && typeof selectedPolicyData.faceAmount === 'number' ?
                      selectedPolicyData.faceAmount.toLocaleString() :
                      '350,000') :
                    '350,000'}
                </p>
                <div className="mt-2">
                  <p className="text-sm text-gray-500 dark:text-gray-400 font-medium">Current Policy Year</p>
                  <p className="text-base font-bold text-gray-900 dark:text-white">
                    {calculatePolicyYear}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </Card>
      )}

      {/* Action Buttons - Only Show After Policy Selection */}
      {selectedPolicyData && (
        <div className="flex justify-end space-x-4">
          <Button
            variant="secondary"
            onClick={() => setShowDisclosurePopup(true)}
            className="flex items-center space-x-2 px-6 py-3 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors font-medium"
          >
            <Info className="w-5 h-5" />
            <span>Disclosure</span>
          </Button>

          <Button
            variant="primary"
            onClick={() => setActiveTab('analysis-reports')}
            className="flex items-center space-x-2 px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors font-medium"
          >
            <FileText className="w-5 h-5" />
            <span>Get Illustration Reports</span>
          </Button>
        </div>
      )}

      {/* No Scenarios Saved Message - Only Show After Policy Selection */}
      {selectedPolicyData && displayScenarios.length === 0 && (
        <Card className="bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-yellow-100 dark:bg-yellow-900/40 rounded-full flex items-center justify-center">
              <span className="text-yellow-600 dark:text-yellow-400 text-lg font-bold">!</span>
            </div>
            <div className="flex-1">
              <h3 className="text-lg font-semibold text-yellow-800 dark:text-yellow-200">No Scenarios Saved</h3>
              <p className="text-yellow-700 dark:text-yellow-300 mb-3">
                No scenarios have been saved yet. Create scenarios to see the interactive chart visualization.
              </p>
              <div className="flex space-x-3">
                <Button
                  variant="primary"
                  onClick={() => setActiveTab('illustrations')}
                >
                  Create Scenarios
                </Button>
                <Button
                  variant="secondary"
                  onClick={async () => {
                    // Create a comprehensive demo scenario to show chart functionality
                    const demoScenario = {
                      id: `demo-${Date.now()}`,
                      name: 'Demo Policy Analysis',
                      policyId: selectedPolicyData?.id?.toString() || '',
                      asIsDetails: 'Comprehensive demo scenario showcasing interactive chart with realistic policy data over 20 years',
                      category: 'as-is' as const,
                      keyPoints: [
                        'Type: Demo AS-IS Analysis',
                        'Question: Long-term Policy Performance',
                        'Selected Option: 20-Year Projection with $350K Face Amount',
                        'Selected Option: Annual Premium $2,500',
                        'Selected Option: Retirement Goal Age 65'
                      ],
                      whatIfOptions: [
                        'Demo scenario with comprehensive 20-year data projection',
                        'Face Amount: $350,000',
                        'Annual Premium: $2,500',
                        'Policy Performance Analysis'
                      ],
                      data: {
                        retirementGoalAge: 65,
                        faceAmount: 350000,
                        annualPremium: 2500,
                        projectionYears: 20
                      },
                      createdAt: new Date(),
                      updatedAt: new Date()
                    };

                    try {
                      await addScenario(demoScenario);
                      console.log('✅ Comprehensive demo scenario created for chart demonstration');

                      // Show a brief notification
                      setTimeout(() => {
                        console.log('💡 Demo scenario created! Click on the scenario card below to see the interactive chart.');
                      }, 500);
                    } catch (error) {
                      console.error('❌ Error creating demo scenario:', error);
                    }
                  }}
                  className="bg-purple-600 hover:bg-purple-700 text-white"
                >
                  <BarChart3 className="w-4 h-4 mr-2" />
                  Demo Interactive Chart
                </Button>
              </div>
            </div>
          </div>
        </Card>
      )}

      {/* Policy Selection Required Message */}
      {!selectedPolicyData && (
        <Card className="bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-yellow-100 dark:bg-yellow-900/40 rounded-full flex items-center justify-center">
              <span className="text-yellow-600 dark:text-yellow-400 text-lg font-bold">!</span>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-yellow-800 dark:text-yellow-200">Policy Selection Required</h3>
              <p className="text-yellow-700 dark:text-yellow-300 mb-3">
                Please select a policy first to view your selected scenarios and illustrations.
              </p>
              <Button
                variant="primary"
                onClick={() => setActiveTab('policy-selection')}
              >
                Go to Policy Selection
              </Button>
            </div>
          </div>
        </Card>
      )}

      {/* Scenarios Grid - Only Show After Policy Selection */}
      {selectedPolicyData && displayScenarios.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {displayScenarios.map((scenario) => (
            <ScenarioCard
              key={scenario.id}
              scenario={scenario}
              isSelected={selectedScenario === scenario.id}
              onScenarioClick={handleScenarioClick}
              onDeleteScenario={handleDeleteScenario}
            />
          ))}
        </div>
      )}

      {/* Inline Scenario Details and Table */}
      {selectedScenario && selectedScenarioData ? (
        (() => {
          try {
            return (
              <div className="mt-8 space-y-6">
                {/* NEW: Comparison Section - Above Key Performance Metrics */}
                <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20">
                  <div className="flex items-center justify-between">
                    {/* Left side - Comparison checkbox */}
                    <div className="flex items-center space-x-3">
                      <button
                        onClick={() => setShowComparisonSelected(!showComparisonSelected)}
                        className="flex items-center space-x-2 text-left"
                      >
                        {showComparisonSelected ? (
                          <CheckSquare className="w-5 h-5 text-blue-600" />
                        ) : (
                          <Square className="w-5 h-5 text-gray-400" />
                        )}
                        <div>
                          <h3 className="text-lg font-semibold text-gray-900">
                            Comparison of the Illustration
                          </h3>
                          <p className="text-sm text-gray-600">
                            {showComparisonSelected ? 'Comparison mode enabled' : 'Click to enable comparison mode'}
                          </p>
                        </div>
                      </button>
                    </div>

                    {/* Right side - AI Commentary button */}
                    <Button
                      variant="primary"
                      onClick={async () => {
                        setShowAiCommentaryPopup(true);
                        await handleAICommentary();
                      }}
                      disabled={isLoadingCommentary}
                      className="flex items-center space-x-2 px-6 py-3 bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 text-white rounded-lg transition-all duration-200 font-medium shadow-lg disabled:opacity-50"
                    >
                      {isLoadingCommentary ? (
                        <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                      ) : (
                        <MessageSquare className="w-5 h-5" />
                      )}
                      <span>{isLoadingCommentary ? 'Generating...' : 'AI Commentary'}</span>
                    </Button>
                  </div>
                </Card>

                {/* Key Performance Metrics */}
                <Card>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Key Performance Metrics</h3>
                  {(() => {
                    if (scenarioTableData.length === 0) {
                      return (
                        <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-6">
                          <div className="text-center">
                            <Info className="w-12 h-12 text-yellow-600 dark:text-yellow-400 mx-auto mb-3" />
                            <h4 className="text-lg font-semibold text-yellow-800 dark:text-yellow-200 mb-2">No Metrics Available</h4>
                            <p className="text-yellow-700 dark:text-yellow-300 text-sm">
                              Click on a scenario card to view key performance metrics.
                            </p>
                          </div>
                        </div>
                      );
                    }

                    // Use the service method to calculate metrics from hardcoded data
                    const keyMetrics = calculateKeyMetrics(scenarioTableData);

                    return (
                      <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-5 gap-4">
                        <div className="bg-gradient-to-br from-blue-50 to-blue-100 p-4 rounded-lg border border-blue-200">
                          <div className="text-2xl font-bold text-blue-600">
                            {formatCurrency(keyMetrics.totalPremiums)}
                          </div>
                          <div className="text-sm text-blue-700 font-medium">Total Premiums</div>
                        </div>
                        <div className="bg-gradient-to-br from-green-50 to-green-100 p-4 rounded-lg border border-green-200">
                          <div className="text-2xl font-bold text-green-600">
                            {formatCurrency(keyMetrics.totalOutlay)}
                          </div>
                          <div className="text-sm text-green-700 dark:text-green-300 font-medium">Total Outlay</div>
                        </div>
                        <div className="bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/30 p-4 rounded-lg border border-purple-200 dark:border-purple-700">
                          <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">
                            {formatCurrency(keyMetrics.finalSurrenderValue)}
                          </div>
                          <div className="text-sm text-purple-700 dark:text-purple-300 font-medium">Final Cash Value</div>
                        </div>
                        <div className="bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-900/20 dark:to-orange-800/30 p-4 rounded-lg border border-orange-200 dark:border-orange-700">
                          <div className="text-2xl font-bold text-orange-600 dark:text-orange-400">
                            {formatCurrency(keyMetrics.finalDeathBenefit)}
                          </div>
                          <div className="text-sm text-orange-700 dark:text-orange-300 font-medium">Final Death Benefit</div>
                        </div>
                        <div className="bg-gradient-to-br from-teal-50 to-teal-100 dark:from-teal-900/20 dark:to-teal-800/30 p-4 rounded-lg border border-teal-200 dark:border-teal-700">
                          <div className="text-2xl font-bold text-teal-600 dark:text-teal-400">
                            {Math.abs(keyMetrics.roi).toFixed(1)}%
                          </div>
                          <div className="text-sm text-teal-700 dark:text-teal-300 font-medium">ROI</div>
                        </div>
                      </div>
                    );
                  })()}
                </Card>

                {/* Modern Area Chart Section */}
                {scenarioTableData.length > 0 && (
                  <Card className="bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-900 border-0 shadow-lg">
                    <div className="mb-6">
                      <div className="flex items-center justify-between mb-4">
                        <div className="flex items-center">
                          <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-xl flex items-center justify-center mr-3">
                            <BarChart3 className="w-5 h-5 text-white" />
                          </div>
                          <div>
                            <h3 className="text-xl font-bold text-gray-900 dark:text-white">
                              Policy Value Visualization
                            </h3>
                            <p className="text-sm text-gray-500 dark:text-gray-400">
                              Interactive timeline analysis
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          <div className="px-3 py-1 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 rounded-full text-xs font-medium">
                            {chartData.length} Years
                          </div>
                        </div>
                      </div>
                      <p className="text-gray-600 dark:text-gray-400 text-sm leading-relaxed">
                        Track the evolution of your policy values over time. Each area represents a different financial component,
                        showing how premiums, cash value, and death benefits change throughout the policy lifecycle.
                      </p>
                    </div>
                    <div className="bg-white dark:bg-gray-800 rounded-xl p-4 shadow-inner">
                      {renderAreaChart()}
                    </div>
                  </Card>
                )}

                {/* Enhanced Detailed Illustration Table */}
                <Card>
                  <div className="flex items-center justify-between mb-6">
                    <div className="flex flex-col">
                      <h3 className="text-xl font-bold text-gray-900 dark:text-white flex items-center">
                        <BarChart3 className="w-6 h-6 text-blue-600 mr-2" />
                        {(() => {
                          const scenario = displayScenarios.find(s => s.id === selectedScenario);
                          if (scenario) {
                            const config = getDetailedTableConfig(scenario);
                            return config.title;
                          }
                          return 'Detailed Analysis';
                        })()}
                      </h3>
                      {/* Scenario Indicator */}
                      {(() => {
                        const scenario = displayScenarios.find(s => s.id === selectedScenario);
                        if (scenario) {
                          return (
                            <div className="flex items-center mt-1">
                              <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                              <span className="text-sm text-gray-600 dark:text-gray-400">
                                Displaying data for: <span className="font-medium text-gray-900 dark:text-white">{scenario.name || 'Selected Scenario'}</span>
                              </span>
                            </div>
                          );
                        }
                        return null;
                      })()}
                    </div>
                  </div>

                  {/* Scrollable Table Container */}
                  <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
                    {detailedTableData.length > 0 && tableColumns.length > 0 ? (
                      <div className="overflow-auto" style={{ maxHeight: '600px' }}>
                        <table className="w-full min-w-max">
                          <thead className="bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-700 sticky top-0 z-10">
                            <tr>
                              <th className="px-3 py-3 text-left font-bold text-gray-700 dark:text-gray-200 text-xs border-r border-gray-200 dark:border-gray-600 bg-gray-100 dark:bg-gray-700">#</th>
                              {tableColumns.map((column, index) => (
                                <th
                                  key={index}
                                  className="px-3 py-3 text-left font-bold text-gray-700 dark:text-gray-200 text-xs border-r border-gray-200 dark:border-gray-600 whitespace-nowrap"
                                  style={{ minWidth: `${column.width || 120}px` }}
                                >
                                  {column.header}
                                </th>
                              ))}
                            </tr>
                          </thead>
                          <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                            {detailedTableData.slice(0, 25).map((row, rowIndex) => (
                              <tr key={rowIndex} className="hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                                <td className="px-3 py-2 text-gray-700 dark:text-gray-200 font-medium text-xs border-r border-gray-200 dark:border-gray-600 bg-gray-50 dark:bg-gray-700/50 sticky left-0">
                                  {rowIndex + 1}
                                </td>
                                {tableColumns.map((column, colIndex) => (
                                  <td
                                    key={colIndex}
                                    className={`px-3 py-2 text-gray-700 dark:text-gray-200 text-xs border-r border-gray-200 dark:border-gray-600 whitespace-nowrap ${
                                      column.isCurrency ? 'table-cell-currency monospace-numbers' :
                                      column.key === 'interestRate' || column.key === 'loanInterestRate' ? 'table-cell-number monospace-numbers' :
                                      'table-cell-text'
                                    }`}
                                  >
                                    {column.isCurrency && row[column.key] !== null && row[column.key] !== undefined ? (
                                      <div className="currency-container">
                                        <span className="currency-symbol">$</span>
                                        <span className="currency-amount monospace-numbers">
                                          {formatCurrencyForTable(row[column.key]).amount}
                                        </span>
                                      </div>
                                    ) : column.key === 'interestRate' || column.key === 'loanInterestRate' ? (
                                      <span className="monospace-numbers">{formatPercentage(row[column.key])}</span>
                                    ) : row[column.key] !== null && row[column.key] !== undefined ? (
                                      row[column.key]
                                    ) : (
                                      '-'
                                    )}
                                  </td>
                                ))}
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    ) : (
                      <div className="p-8 text-center">
                        <BarChart3 className="w-12 h-12 text-gray-400 mx-auto mb-3" />
                        <p className="text-gray-500 dark:text-gray-400">No detailed table data available</p>
                      </div>
                    )}
                  </div>
                </Card>
              </div>
            );
          } catch (err) {
            return (
              <div className="mt-8 p-6 bg-red-50 border border-red-200 rounded-lg text-red-700">
                <h3 className="text-lg font-semibold mb-2">Error displaying scenario details</h3>
                <p>There was a problem rendering the selected scenario. Please try another scenario or contact support if the problem persists.</p>
              </div>
            );
          }
        })()
      ) : selectedScenario ? (
        <div className="mt-8 p-6 bg-yellow-50 border border-yellow-200 rounded-lg text-yellow-700">
          <h3 className="text-lg font-semibold mb-2">Scenario Not Found</h3>
          <p>The selected scenario could not be found. It may have been deleted or is invalid.</p>
        </div>
      ) : null}

      {/* AI Commentary Popup Modal */}
      {showAiCommentaryPopup && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[80vh] flex flex-col">
            {/* Modal Header */}
            <div className="flex items-center justify-between p-4 border-b border-gray-200 flex-shrink-0">
              <h2 className="text-xl font-bold text-gray-900 flex items-center">
                <MessageSquare className="w-5 h-5 text-purple-600 mr-2" />
                AI Commentary & Analysis
              </h2>
              <button
                onClick={() => setShowAiCommentaryPopup(false)}
                className="p-1 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <X className="w-5 h-5 text-gray-500 dark:text-gray-400" />
              </button>
            </div>

            {/* Modal Content - Scrollable */}
            <div className="flex-1 overflow-y-auto p-6">
              {isLoadingCommentary ? (
                <div className="flex items-center justify-center py-12">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto mb-4"></div>
                    <p className="text-gray-600">Generating AI commentary...</p>
                  </div>
                </div>
              ) : commentaryError ? (
                <div className="bg-red-50 border border-red-200 rounded-lg p-6">
                  <div className="flex items-center mb-3">
                    <AlertTriangle className="w-5 h-5 text-red-600 mr-2" />
                    <h3 className="text-lg font-semibold text-red-900">Error</h3>
                  </div>
                  <p className="text-red-700">{commentaryError}</p>
                  <button
                    onClick={handleAICommentary}
                    className="mt-4 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                  >
                    Try Again
                  </button>
                </div>
              ) : aiCommentary ? (
                <div className="space-y-6">
                  {/* Analysis Summary */}
                  <div className="bg-gradient-to-r from-purple-50 to-indigo-50 dark:from-purple-900/20 dark:to-indigo-900/20 rounded-lg p-6">
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center">
                      <BarChart3 className="w-5 h-5 text-purple-600 mr-2" />
                      Policy Performance Analysis
                    </h3>
                    <div className="text-sm text-gray-700 dark:text-gray-300">
                      <p>{aiCommentary.summary}</p>
                    </div>
                  </div>

                  {/* AI Recommendations */}
                  <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg p-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center">
                      <Info className="w-5 h-5 text-green-600 mr-2" />
                      AI Recommendations
                    </h3>
                    <div className="space-y-2 text-sm text-gray-700">
                      {aiCommentary.recommendations.map((recommendation, index) => (
                        <div key={index} className="flex items-start space-x-2">
                          <div className="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0"></div>
                          <p>{recommendation}</p>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Risk Considerations */}
                  <div className="bg-gradient-to-r from-amber-50 to-orange-50 rounded-lg p-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center">
                      <AlertTriangle className="w-5 h-5 text-amber-600 mr-2" />
                      Risk Considerations
                    </h3>
                    <div className="space-y-2 text-sm text-gray-700">
                      {aiCommentary.risks.map((risk, index) => (
                        <div key={index} className="flex items-start space-x-2">
                          <div className="w-2 h-2 bg-amber-500 rounded-full mt-2 flex-shrink-0"></div>
                          <p>{risk}</p>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Key Insights */}
                  <div className="bg-gradient-to-r from-blue-50 to-cyan-50 rounded-lg p-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center">
                      <Info className="w-5 h-5 text-blue-600 mr-2" />
                      Key Insights
                    </h3>
                    <div className="space-y-2 text-sm text-gray-700">
                      {aiCommentary.keyInsights.map((insight, index) => (
                        <div key={index} className="flex items-start space-x-2">
                          <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                          <p>{insight}</p>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-center py-12">
                  <MessageSquare className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-600">Click "AI Commentary" to generate analysis</p>
                </div>
              )}
            </div>

            {/* Modal Footer */}
            <div className="flex justify-end space-x-3 p-4 border-t border-gray-200 dark:border-gray-700 flex-shrink-0 bg-gray-50 dark:bg-gray-800">
              <Button
                variant="secondary"
                onClick={() => setShowAiCommentaryPopup(false)}
                className="px-6 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors font-medium"
              >
                Close
              </Button>
              <Button
                variant="primary"
                onClick={() => {
                  setShowAiCommentaryPopup(false);
                  setActiveTab('analysis-reports');
                }}
                className="px-6 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors font-medium"
              >
                Generate Full Report
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Disclosure Popup Modal */}
      {showDisclosurePopup && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-4xl w-full max-h-[80vh] flex flex-col">
            {/* Modal Header */}
            <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700 flex-shrink-0">
              <h2 className="text-xl font-bold text-gray-900 dark:text-white flex items-center">
                <Info className="w-5 h-5 text-blue-600 mr-2" />
                Policy Disclosure Information
              </h2>
              <button
                onClick={() => setShowDisclosurePopup(false)}
                className="p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
              >
                <X className="w-5 h-5 text-gray-500 dark:text-gray-400" />
              </button>
            </div>

            {/* Modal Content - Scrollable */}
            <div className="flex-1 overflow-y-auto p-4">
              {loadingDisclosure ? (
                <div className="flex items-center justify-center py-12">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4"></div>
                    <h4 className="text-xl font-semibold text-gray-700 mb-2">Loading Disclosure Data</h4>
                    <p className="text-gray-600">
                      Please wait while we fetch the policy disclosure information...
                    </p>
                  </div>
                </div>
              ) : disclosureError ? (
                <div className="bg-red-50 border border-red-200 rounded-lg p-6">
                  <div className="text-center">
                    <AlertTriangle className="w-16 h-16 text-red-600 mx-auto mb-4" />
                    <h4 className="text-xl font-semibold text-red-800 dark:text-red-200 mb-2">Error Loading Disclosure Data</h4>
                    <p className="text-red-700 dark:text-red-300 mb-4">{disclosureError}</p>
                    <button
                      onClick={async () => {
                        // Use the service method to retry disclosure data fetch
                        await initializeDisclosureData(
                          selectedCustomerData,
                          selectedPolicyData,
                          setLoadingDisclosure,
                          setDisclosureError,
                          setDisclosureData
                        );
                      }}
                      className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors font-medium"
                    >
                      Retry
                    </button>
                  </div>
                </div>
              ) : disclosureData.length === 0 ? (
                <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-6">
                  <div className="text-center">
                    <Info className="w-16 h-16 text-yellow-600 dark:text-yellow-400 mx-auto mb-4" />
                    <h4 className="text-xl font-semibold text-yellow-800 dark:text-yellow-200 mb-2">No Disclosure Data Available</h4>
                    <p className="text-yellow-700 dark:text-yellow-300">
                      No disclosure information is available from the backend for this policy.
                    </p>
                  </div>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="w-full border-collapse border border-gray-300 dark:border-gray-600">
                    <thead>
                      <tr className="bg-gray-50 dark:bg-gray-700">
                        <th className="border border-gray-300 dark:border-gray-600 px-3 py-2 text-center font-bold text-gray-700 dark:text-gray-200 w-16 text-sm">
                          S.No
                        </th>
                        <th className="border border-gray-300 dark:border-gray-600 px-3 py-2 text-center font-bold text-gray-700 dark:text-gray-200 w-48 text-sm">
                          Type
                        </th>
                        <th className="border border-gray-300 dark:border-gray-600 px-3 py-2 text-center font-bold text-gray-700 dark:text-gray-200 text-sm">
                          Disclosure
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      {disclosureData.map((item) => (
                        <tr key={item.DISCLOSURE_ID} className="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                          <td className="border border-gray-300 dark:border-gray-600 px-3 py-2 text-center font-medium text-gray-900 dark:text-white text-sm">
                            {item.DISCLOSURE_ID}
                          </td>
                          <td className="border border-gray-300 dark:border-gray-600 px-3 py-2 text-center font-semibold text-gray-900 dark:text-white text-sm">
                            {item.TYPE}
                          </td>
                          <td className="border border-gray-300 dark:border-gray-600 px-3 py-2 text-justify text-gray-700 dark:text-gray-300 leading-relaxed text-sm">
                            {item.DISCLOSURE}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </div>

            {/* Modal Footer - Always Visible */}
            <div className="flex justify-end p-4 border-t border-gray-200 dark:border-gray-700 flex-shrink-0 bg-gray-50 dark:bg-gray-800">
              <Button
                variant="primary"
                onClick={() => setShowDisclosurePopup(false)}
                className="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors font-medium"
              >
                Close
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SelectedScenarios;