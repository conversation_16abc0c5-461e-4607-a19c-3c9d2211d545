import React, { useState, useEffect } from 'react';
import Card from '../common/Card';
import Button from '../common/Button';
import Input from '../common/Input';
import Notification from '../common/Notification';
import { Save, RotateCcw } from 'lucide-react';
import { useDashboard } from '../../contexts/DashboardContext';
import {
  saveLoanRepaymentIllustration,
  validateLoanRepaymentData,
  type LoanRepaymentIllustrationData
} from '../../services/loanRepaymentService';
import { formatCurrency } from '../../utils/currencyFormatter';
import {
  isSafari,
  safeDateParse,
  safeNumberParse,
  safeCurrencyFormat,
  runCompatibilityTests
} from '../../utils/browserCompatibility';

// Add the type for the table rows
interface LoanRepaymentTableRow {
  age: number;
  policyYear: string;
  calendarYear: number;
  repaymentAmount: number;
}

const LoanRepaymentPage: React.FC = () => {
  const { selectedCustomerData, selectedPolicyData, setActiveTab, addScenario } = useDashboard();

  // Debug function to log all available data and run compatibility tests
  const debugDataAvailability = () => {
    console.log('=== LOAN REPAYMENT PAGE DEBUG ===');
    console.log('selectedCustomerData:', selectedCustomerData);
    console.log('selectedPolicyData:', selectedPolicyData);
    console.log('Is Safari:', isSafari());

    // Run comprehensive compatibility tests
    runCompatibilityTests();

    if (selectedCustomerData) {
      console.log('Available customer data keys:', Object.keys(selectedCustomerData));
      Object.keys(selectedCustomerData).forEach(key => {
        console.log(`Customer ${key}:`, (selectedCustomerData as any)[key]);
      });
    }

    if (selectedPolicyData) {
      console.log('Available policy data keys:', Object.keys(selectedPolicyData));
      Object.keys(selectedPolicyData).forEach(key => {
        console.log(`Policy ${key}:`, (selectedPolicyData as any)[key]);
      });
    }
    console.log('=== END DEBUG ===');
  };

  // Run debug on component mount and when data changes
  useEffect(() => {
    debugDataAvailability();
  }, [selectedCustomerData, selectedPolicyData]);

  // Save scenario state
  const [isSaving, setIsSaving] = useState(false);
  const [notification, setNotification] = useState<{ message: string; type?: 'success' | 'error' } | null>(null);

  // New loan repayment strategy state
  const [repaymentType, setRepaymentType] = useState('');

  // Option 1: Change to New Loan Repayment amount now
  const [newLoanAmount, setNewLoanAmount] = useState('');

  // Option 3: Lump Sum (One-time premium) now
  const [lumpSumAmount, setLumpSumAmount] = useState('');

  // Add at the top, after imports
  const [loanRepaymentByYearData, setLoanRepaymentByYearData] = useState<{
    selectedTypes: { age: boolean; policyYear: boolean; calendarYear: boolean };
    ageRange: { start: number; end: number };
    policyYearRange: { start: number; end: number };
    calendarYearRange: { start: number; end: number };
    isEditing: boolean;
    tableData: LoanRepaymentTableRow[];
  }>(
    {
      selectedTypes: { age: false, policyYear: false, calendarYear: false },
      ageRange: { start: 40, end: 100 },
      policyYearRange: { start: 1, end: 100 },
      calendarYearRange: { start: 2024, end: 2100 },
      isEditing: false,
      tableData: []
    }
  );

  // Initialize ranges with actual current values when customer/policy data changes
  useEffect(() => {
    const currentAge = calculateCurrentAge();
    const currentPolicyYear = calculateCurrentPolicyYear();
    const currentYear = getCurrentYear();

    setLoanRepaymentByYearData(prev => ({
      ...prev,
      ageRange: {
        start: currentAge,
        end: 100
      },
      policyYearRange: {
        start: currentPolicyYear,
        end: 100
      },
      calendarYearRange: {
        start: currentYear,
        end: 2100
      }
    }));
  }, [selectedCustomerData, selectedPolicyData]);

  // Helper functions for current age/year
  const getCurrentYear = () => new Date().getFullYear();
  
  /**
   * Universal date parsing function that works across all browsers including Safari
   * Uses the safeDateParse utility for enhanced compatibility
   */
  const parseDate = (dateInput: any): Date | null => {
    console.log('parseDate: Processing date input:', dateInput);

    // Use the safe date parsing utility
    const result = safeDateParse(dateInput);

    if (result) {
      console.log('parseDate: Successfully parsed date:', result);
    } else {
      console.warn('parseDate: Failed to parse date:', dateInput);
    }

    return result;
  };



  // Calculate current age from DOB with enhanced Safari compatibility
  const calculateCurrentAge = (): number => {
    console.log('calculateCurrentAge: Starting calculation...');

    // Try to find DOB from multiple possible fields
    const possibleDobFields = [
      selectedCustomerData?.details?.DOB,
      (selectedCustomerData as any)?.dateOfBirth,
      (selectedCustomerData as any)?.dob,
      (selectedPolicyData as any)?.customerDob,
      (selectedPolicyData as any)?.dateOfBirth
    ];

    console.log('calculateCurrentAge: Possible DOB fields:', possibleDobFields);

    let dobValue = null;
    let dobSource = '';

    // Find the first valid DOB
    for (let i = 0; i < possibleDobFields.length; i++) {
      if (possibleDobFields[i]) {
        dobValue = possibleDobFields[i];
        dobSource = `Field ${i}`;
        console.log(`calculateCurrentAge: Found DOB in ${dobSource}:`, dobValue);
        break;
      }
    }

    if (!dobValue) {
      console.warn('calculateCurrentAge: No DOB found in any field, using default age 40');
      return 40;
    }

    const birthDate = parseDate(dobValue);
    console.log('calculateCurrentAge: Parsed birth date:', birthDate, 'from', dobSource);

    if (!birthDate) {
      console.error('calculateCurrentAge: Failed to parse DOB:', dobValue);
      return 40;
    }

    const today = new Date();
    today.setHours(0, 0, 0, 0);
    console.log('calculateCurrentAge: Current date:', today);

    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();

    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }

    console.log('calculateCurrentAge: Calculated age:', age);

    // Validate the result
    if (!Number.isInteger(age) || age < 0 || age > 150) {
      console.error('calculateCurrentAge: Invalid age calculated:', age);
      return 40;
    }

    return age;
  };

  // Calculate current policy year from issue date with enhanced Safari compatibility
  const calculateCurrentPolicyYear = (): number => {
    console.log('calculateCurrentPolicyYear: Starting calculation...');

    // Try to find issue date from multiple possible fields
    const possibleDateFields = [
      (selectedPolicyData as any)?.issueDate,
      (selectedPolicyData as any)?.policyStartDate,
      (selectedPolicyData as any)?.startDate,
      (selectedPolicyData as any)?.effectiveDate,
      (selectedPolicyData as any)?.policyDate,
      (selectedPolicyData as any)?.inceptionDate,
      (selectedPolicyData as any)?.createdDate,
      (selectedPolicyData as any)?.dateIssued,
      (selectedCustomerData as any)?.policyStartDate,
      (selectedCustomerData as any)?.issueDate
    ];

    console.log('calculateCurrentPolicyYear: Possible date fields:', possibleDateFields);

    let issueDate = null;
    let dateSource = '';

    // Find the first valid date
    for (let i = 0; i < possibleDateFields.length; i++) {
      if (possibleDateFields[i]) {
        issueDate = possibleDateFields[i];
        dateSource = `Field ${i}`;
        console.log(`calculateCurrentPolicyYear: Found date in ${dateSource}:`, issueDate);
        break;
      }
    }

    if (!issueDate) {
      console.warn('calculateCurrentPolicyYear: No issue date found in any field, using default policy year 1');
      return 1;
    }

    const parsedIssueDate = parseDate(issueDate);
    console.log('calculateCurrentPolicyYear: Parsed issue date:', parsedIssueDate, 'from', dateSource);

    if (!parsedIssueDate) {
      console.error('calculateCurrentPolicyYear: Failed to parse issue date:', issueDate);
      return 1;
    }

    // Get current date and normalize
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    console.log('calculateCurrentPolicyYear: Current date:', today);

    // Calculate year difference
    let yearDiff = today.getFullYear() - parsedIssueDate.getFullYear();
    console.log('calculateCurrentPolicyYear: Initial year difference:', yearDiff);

    // Create anniversary date for this year
    const anniversaryThisYear = new Date(parsedIssueDate);
    anniversaryThisYear.setFullYear(today.getFullYear());
    anniversaryThisYear.setHours(0, 0, 0, 0);
    console.log('calculateCurrentPolicyYear: Anniversary this year:', anniversaryThisYear);

    // If today is before this year's anniversary, we're still in the previous policy year
    if (today < anniversaryThisYear) {
      yearDiff--;
      console.log('calculateCurrentPolicyYear: Before anniversary, adjusted year diff:', yearDiff);
    }

    const currentPolicyYear = yearDiff + 1;
    console.log('calculateCurrentPolicyYear: Final policy year:', currentPolicyYear);

    // Validate the result
    if (!Number.isInteger(currentPolicyYear) || currentPolicyYear < 1) {
      console.error('calculateCurrentPolicyYear: Invalid policy year calculated:', currentPolicyYear);
      return 1;
    }

    if (currentPolicyYear > 100) {
      console.warn('calculateCurrentPolicyYear: Policy year seems too high:', currentPolicyYear);
      return 1;
    }

    return currentPolicyYear;
  };

  // Generate table data based on selected types and ranges
  const generateLoanRepaymentTableData = () => {
    const { selectedTypes, ageRange, policyYearRange, calendarYearRange } = loanRepaymentByYearData;
    let startYear = 0, endYear = 0;
    
    // Determine which range to use based on selected types
    if (selectedTypes.age) {
      const currentAge = calculateCurrentAge();
      const currentCalendarYear = getCurrentYear();
      startYear = currentCalendarYear + (ageRange.start - currentAge);
      endYear = currentCalendarYear + (ageRange.end - currentAge);
    } else if (selectedTypes.policyYear) {
      const currentPolicyYear = calculateCurrentPolicyYear();
      const currentCalendarYear = getCurrentYear();
      startYear = currentCalendarYear + (policyYearRange.start - currentPolicyYear);
      endYear = currentCalendarYear + (policyYearRange.end - currentPolicyYear);
    } else if (selectedTypes.calendarYear) {
      startYear = calendarYearRange.start;
      endYear = calendarYearRange.end;
    } else {
      // No type selected, return empty array
      return [];
    }
    
    if (startYear === 0 || endYear === 0 || startYear > endYear) {
      // Return empty array if no valid range
      return [];
    }
    
    const currentAge = calculateCurrentAge();
    const currentCalendarYear = getCurrentYear();
    const currentPolicyYear = calculateCurrentPolicyYear();
    const data = [];
    const maxEntries = 12;
    const totalYears = endYear - startYear + 1;
    const actualEndYear = totalYears > maxEntries ? startYear + maxEntries - 1 : endYear;
    
    for (let year = startYear; year <= actualEndYear; year++) {
      data.push({
        age: currentAge + (year - currentCalendarYear),
        policyYear: `Year ${currentPolicyYear + (year - currentCalendarYear)}`,
        calendarYear: year,
        repaymentAmount: 0 // Default value
      });
    }
    return data;
  };

  // Initialize table data on component mount
  useEffect(() => {
    setLoanRepaymentByYearData(prev => ({
      ...prev,
      tableData: generateLoanRepaymentTableData()
    }));
  }, []);

  // Update table data when selections change
  useEffect(() => {
    setLoanRepaymentByYearData(prev => ({
      ...prev,
      tableData: generateLoanRepaymentTableData()
    }));
    // eslint-disable-next-line
  }, [loanRepaymentByYearData.selectedTypes, loanRepaymentByYearData.ageRange, loanRepaymentByYearData.policyYearRange, loanRepaymentByYearData.calendarYearRange, repaymentType]);

  // Reset all scenario state
  const handleResetScenarios = () => {
    setRepaymentType('');
    setNewLoanAmount('');
    setLumpSumAmount('');
    // Reset table data
    setLoanRepaymentByYearData({
      selectedTypes: { age: false, policyYear: false, calendarYear: false },
      ageRange: { start: 40, end: 100 },
      policyYearRange: { start: 1, end: 100 },
      calendarYearRange: { start: 2024, end: 2100 },
      isEditing: false,
      tableData: []
    });
    setNotification({ message: 'All loan repayment scenarios have been reset!', type: 'success' });
  };

  // Handle option selection
  const handleOptionSelect = (option: string) => {
    if (repaymentType === option) {
      setRepaymentType(''); // Deselect if already selected
    } else {
      setRepaymentType(option);
    }

    // Special handling for modify-age option
    if (option === 'modify-age') {
      // Auto-select Age checkbox and set default range to make table visible
      setLoanRepaymentByYearData(prev => ({
        ...prev,
        selectedTypes: { age: true, policyYear: false, calendarYear: false },
        ageRange: { start: calculateCurrentAge(), end: calculateCurrentAge() + 10 } // Show 10 years by default
      }));
    }
  };

  // Save scenario function with enhanced error handling and Safari compatibility
  const saveScenario = async () => {
    console.log('saveScenario: Starting save process...');

    if (!selectedCustomerData || !selectedPolicyData) {
      const errorMsg = 'Please select a customer and policy first!';
      console.error('saveScenario:', errorMsg);
      setNotification({ message: errorMsg, type: 'error' });
      return;
    }

    // Validate that at least one option is selected
    if (!repaymentType) {
      const errorMsg = 'Please select a loan repayment option before saving!';
      console.error('saveScenario:', errorMsg);
      setNotification({ message: errorMsg, type: 'error' });
      return;
    }

    // Additional validation for specific repayment types using safe parsing
    if (repaymentType === 'new-loan-amount') {
      const amount = safeNumberParse(newLoanAmount);
      if (!newLoanAmount || amount === null || amount <= 0) {
        const errorMsg = 'Please enter a valid new loan amount greater than 0!';
        console.error('saveScenario:', errorMsg);
        setNotification({ message: errorMsg, type: 'error' });
        return;
      }
    }

    if (repaymentType === 'lump-sum') {
      const amount = safeNumberParse(lumpSumAmount);
      if (!lumpSumAmount || amount === null || amount <= 0) {
        const errorMsg = 'Please enter a valid lump sum amount greater than 0!';
        console.error('saveScenario:', errorMsg);
        setNotification({ message: errorMsg, type: 'error' });
        return;
      }
    }

    if (repaymentType === 'modify-age') {
      if (!loanRepaymentByYearData.tableData || loanRepaymentByYearData.tableData.length === 0) {
        const errorMsg = 'Please configure the repayment schedule before saving!';
        console.error('saveScenario:', errorMsg);
        setNotification({ message: errorMsg, type: 'error' });
        return;
      }

      // Check if at least one repayment amount is greater than 0
      const hasValidRepayments = loanRepaymentByYearData.tableData.some(row =>
        row.repaymentAmount && row.repaymentAmount > 0
      );

      if (!hasValidRepayments) {
        const errorMsg = 'Please enter at least one repayment amount greater than 0 in the schedule!';
        console.error('saveScenario:', errorMsg);
        setNotification({ message: errorMsg, type: 'error' });
        return;
      }
    }

    setIsSaving(true);
    try {
      // Get policy ID from selected policy data with enhanced parsing
      let policyId: number;

      // Try multiple ways to get the policy ID
      if (selectedPolicyData.id) {
        policyId = parseInt(String(selectedPolicyData.id), 10);
      } else if ((selectedPolicyData as any).policyId) {
        policyId = parseInt(String((selectedPolicyData as any).policyId), 10);
      } else if (selectedCustomerData.customerId) {
        policyId = parseInt(String(selectedCustomerData.customerId), 10);
      } else if ((selectedCustomerData as any).id) {
        policyId = parseInt(String((selectedCustomerData as any).id), 10);
      } else {
        throw new Error('Policy ID not found in selected data');
      }

      if (!policyId || isNaN(policyId) || policyId <= 0) {
        throw new Error('Invalid Policy ID found in selected data');
      }

      console.log('saveScenario: Using policy ID:', policyId);

      // Prepare data for backend API with enhanced number parsing
      const loanRepaymentData: LoanRepaymentIllustrationData = {
        policy_id: policyId,
        repayment_type: repaymentType,
        new_loan_amount: newLoanAmount ? (() => {
          const parsed = safeNumberParse(newLoanAmount);
          return parsed !== null ? Math.abs(parsed) : undefined;
        })() : undefined,
        lump_sum_amount: lumpSumAmount ? (() => {
          const parsed = safeNumberParse(lumpSumAmount);
          return parsed !== null ? Math.abs(parsed) : undefined;
        })() : undefined,
        schedule_data: repaymentType === 'modify-age' && loanRepaymentByYearData.tableData.length > 0
          ? loanRepaymentByYearData.tableData.map(row => {
              // Enhanced parsing for policy year
              let policyYearNum: number;
              if (typeof row.policyYear === 'string') {
                const yearMatch = row.policyYear.match(/\d+/);
                policyYearNum = yearMatch ? parseInt(yearMatch[0], 10) : 1;
              } else {
                policyYearNum = Number(row.policyYear) || 1;
              }

              return {
                age: Number(row.age) || 0,
                policy_year: policyYearNum,
                calendar_year: Number(row.calendarYear) || new Date().getFullYear(),
                repayment_amount: Number(row.repaymentAmount) || 0
              };
            })
          : undefined,
        age_range: loanRepaymentByYearData.selectedTypes.age
          ? {
              start: Number(loanRepaymentByYearData.ageRange.start) || 0,
              end: Number(loanRepaymentByYearData.ageRange.end) || 0
            }
          : undefined,
        policy_year_range: loanRepaymentByYearData.selectedTypes.policyYear
          ? {
              start: Number(loanRepaymentByYearData.policyYearRange.start) || 0,
              end: Number(loanRepaymentByYearData.policyYearRange.end) || 0
            }
          : undefined,
        calendar_year_range: loanRepaymentByYearData.selectedTypes.calendarYear
          ? {
              start: Number(loanRepaymentByYearData.calendarYearRange.start) || 0,
              end: Number(loanRepaymentByYearData.calendarYearRange.end) || 0
            }
          : undefined,
        selected_type: loanRepaymentByYearData.selectedTypes.age ? 'age'
          : loanRepaymentByYearData.selectedTypes.policyYear ? 'policyYear'
          : loanRepaymentByYearData.selectedTypes.calendarYear ? 'calendarYear'
          : undefined
      };

      console.log('saveScenario: Prepared loan repayment data:', loanRepaymentData);

      // Validate data before sending
      const validationErrors = validateLoanRepaymentData(loanRepaymentData);
      if (validationErrors.length > 0) {
        setNotification({ message: `Validation errors: ${validationErrors.join(', ')}`, type: 'error' });
        return;
      }

      // Calculate current age and policy year (you may need to adjust this based on your data)
      const currentAge = calculateCurrentAge();
      const currentPolicyYear = calculateCurrentPolicyYear();

      // Save to backend
      const result = await saveLoanRepaymentIllustration(loanRepaymentData, currentAge, currentPolicyYear);

      if (result.status === 'SUCCESS') {
        setNotification({ message: 'Loan Repayment illustration is saved successfully', type: 'success' });

        // ✅ Add scenario to current session for immediate display (maintains fresh start)
        try {
          // Helper function to get selected option description and name
          const getScenarioDetails = () => {
            switch (loanRepaymentData.repayment_type) {
              case 'new-loan-amount':
                return {
                  name: `New Loan Amount: ${formatCurrency(loanRepaymentData.new_loan_amount || 0)}`,
                  description: `New Loan Amount: ${formatCurrency(loanRepaymentData.new_loan_amount || 0)}`,
                  asIsDetails: 'Loan Repayment Analysis - New Loan Amount'
                };
              case 'lump-sum':
                return {
                  name: `Lump Sum Payment: ${formatCurrency(loanRepaymentData.lump_sum_amount || 0)}`,
                  description: `Lump Sum Payment: ${formatCurrency(loanRepaymentData.lump_sum_amount || 0)}`,
                  asIsDetails: 'Loan Repayment Analysis - Lump Sum Payment'
                };
              case 'interest-only':
                return {
                  name: 'Interest Only Payment',
                  description: 'Interest Only Payment',
                  asIsDetails: 'Loan Repayment Analysis - Interest Only'
                };
              case 'modify-age':
                return {
                  name: `Modified Repayment Schedule (${loanRepaymentData.schedule_data?.length || 0} entries)`,
                  description: `Modified Repayment Schedule (${loanRepaymentData.schedule_data?.length || 0} entries)`,
                  asIsDetails: 'Loan Repayment Analysis - Modified Schedule'
                };
              default:
                return {
                  name: loanRepaymentData.repayment_type || 'Unknown Loan Repayment',
                  description: loanRepaymentData.repayment_type || 'Unknown',
                  asIsDetails: 'Loan Repayment Analysis - Unknown Type'
                };
            }
          };

          const scenarioDetails = getScenarioDetails();

          const scenarioData = {
            id: `loan-repayment-${loanRepaymentData.repayment_type}-${Date.now()}`,
            name: scenarioDetails.name,
            policyId: selectedPolicyData?.id?.toString() || '',
            asIsDetails: scenarioDetails.asIsDetails,
            category: 'loan-repayment' as const,
            keyPoints: [
              `Type: Loan Repayment`,
              `Question: Loan Repayment Strategy`,
              `Selected Option: ${scenarioDetails.description}`,
            ],
            whatIfOptions: [scenarioDetails.description],
            data: {
              ...loanRepaymentData,
              scenarioType: loanRepaymentData.repayment_type
            },
            createdAt: new Date(),
            updatedAt: new Date()
          };

          await addScenario(scenarioData);
          setNotification({ message: 'Loan repayment scenario saved successfully!', type: 'success' });
          console.log('✅ Loan Repayment scenario added to current session');
        } catch (error) {
          console.error('❌ Error adding scenario to session:', error);
          setNotification({ message: 'Error saving loan repayment scenario', type: 'error' });
        }
      } else {
        throw new Error(result.message || 'Failed to save to database');
      }
    } catch (error) {
      console.error('Error saving scenario:', error);
      setNotification({ message: `Error saving scenario: ${error instanceof Error ? error.message : 'Unknown error'}`, type: 'error' });
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div className="space-y-6">
      {notification && (
        <Notification
          message={notification.message}
          type={notification.type}
          onClose={() => setNotification(null)}
        />
      )}

      {/* Show message if no policy is selected */}
      {(!selectedCustomerData || !selectedPolicyData) ? (
        <Card className="bg-yellow-50 border-yellow-200">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
              <span className="text-yellow-600 text-sm">!</span>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-yellow-800">No Policy Selected</h3>
              <p className="text-yellow-700">
                Please go to the Policy Selection tab first to search and select a customer policy before configuring the Loan Repayment illustration.
              </p>
              <Button
                onClick={() => setActiveTab('policy-selection')}
                variant="outline"
                className="mt-3 border-yellow-300 text-yellow-700 hover:bg-yellow-100"
              >
                Go to Policy Selection
              </Button>
            </div>
          </div>
        </Card>
      ) : (
        <>
          {/* Scenario Description */}
          <Card className="mb-6 bg-blue-50 border-blue-200">
            <div className="text-black">
              <h3 className="text-lg font-semibold mb-2">Loan Repayment Scenario Overview</h3>
              <p>
                This scenario shows how repaying outstanding policy loans—fully or partially—affects your policy’s future values. It highlights changes in policy debt, cash value recovery, and potential benefits of restoring policy health using the Current Interest Rate.
              </p>
            </div>
          </Card>
          {/* New Loan Repayment Strategy */}
          <Card className="mb-8">
            <h2 className="text-xl font-bold mb-4 text-black">Loan Repayment Strategy (scheduled or lump sum)</h2>
            <div className="space-y-6 pl-6 mt-4">
              {/* Option 1: Change to New Loan Repayment amount now */}
              <div className="space-y-2">
                <label className="flex items-center text-lg font-semibold text-black mb-2">
                  <input
                    type="radio"
                    name="repaymentType"
                    value="new-loan-amount"
                    checked={repaymentType === 'new-loan-amount'}
                    onClick={() => handleOptionSelect('new-loan-amount')}
                    readOnly
                    className="mr-2"
                  />
                  Change to New Loan Repayment amount now
                </label>
                {repaymentType === 'new-loan-amount' && (
                  <div className="grid grid-cols-2 gap-4 mt-2 ml-6">
                    <div>
                      <Input
                        type="number"
                        value={newLoanAmount}
                        onChange={(e) => {
                          // Enhanced number input handling for Safari compatibility
                          const inputValue = e.target.value;
                          // Allow only numbers and decimal points
                          const sanitized = inputValue.replace(/[^0-9.]/g, '');
                          setNewLoanAmount(sanitized);
                        }}
                        onBlur={(e) => {
                          // Format the value on blur for consistency using safe parsing
                          const inputValue = e.target.value;
                          if (inputValue && inputValue.trim() !== '') {
                            const parsed = safeNumberParse(inputValue);
                            if (parsed !== null && parsed >= 0) {
                              setNewLoanAmount(parsed.toString());
                            } else {
                              setNewLoanAmount('');
                            }
                          }
                        }}
                        placeholder="Enter amount (e.g., 50000)"
                        className="w-full p-3 border-2 border-gray-300 rounded-lg font-semibold text-black bg-blue-50"
                        min="0"
                        step="1000"
                      />
                    </div>
                  </div>
                )}
              </div>

              {/* Option 2: Modify the loan repayment Schedule (following FaceAmount pattern) */}
              <div className="space-y-4">
                <label className="flex items-center text-lg font-semibold text-black mb-2">
                  <input
                    type="radio"
                    name="repaymentType"
                    value="modify-age"
                    checked={repaymentType === 'modify-age'}
                    onClick={() => handleOptionSelect('modify-age')}
                    readOnly
                    className="mr-2"
                  />
                  Modify the loan repayment Schedule
                </label>
                {repaymentType === 'modify-age' && (
                  <div className="mt-4 space-y-6">
                    {/* Single Container for Type Selection and Range Controls */}
                    <div className="bg-white p-6 rounded-lg border border-gray-300">
                      {/* Type Selection Checkboxes - only one can be selected */}
                      <div className="grid grid-cols-3 gap-4 mb-6">
                        <label className="flex items-center text-black font-semibold">
                          <input
                            type="checkbox"
                            checked={loanRepaymentByYearData.selectedTypes.age}
                            onChange={e => setLoanRepaymentByYearData(prev => ({ ...prev, selectedTypes: { age: e.target.checked, policyYear: false, calendarYear: false } }))}
                            className="mr-2"
                          />
                          Age
                        </label>
                        <label className="flex items-center text-black font-semibold">
                          <input
                            type="checkbox"
                            checked={loanRepaymentByYearData.selectedTypes.policyYear}
                            onChange={e => setLoanRepaymentByYearData(prev => ({ ...prev, selectedTypes: { age: false, policyYear: e.target.checked, calendarYear: false } }))}
                            className="mr-2"
                          />
                          Policy Year
                        </label>
                        <label className="flex items-center text-black font-semibold">
                          <input
                            type="checkbox"
                            checked={loanRepaymentByYearData.selectedTypes.calendarYear}
                            onChange={e => setLoanRepaymentByYearData(prev => ({ ...prev, selectedTypes: { age: false, policyYear: false, calendarYear: e.target.checked } }))}
                            className="mr-2"
                          />
                          Calendar Year
                        </label>
                      </div>

                      {/* Age Range Toggle Bars */}
                      {loanRepaymentByYearData.selectedTypes.age && (
                        <div className="space-y-4">
                          <div className="grid grid-cols-2 gap-6">
                            <div>
                              <label className="block text-sm font-bold text-black mb-2">Start Age</label>
                              <div className="flex items-center space-x-3 bg-white border-2 border-gray-300 rounded-lg p-3">
                                <button
                                  onClick={() => setLoanRepaymentByYearData(prev => ({
                                    ...prev,
                                    ageRange: { ...prev.ageRange, start: Math.max(calculateCurrentAge(), prev.ageRange.start - 1) }
                                  }))}
                                  className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white"
                                  disabled={loanRepaymentByYearData.ageRange.start <= calculateCurrentAge()}
                                >
                                  ◀
                                </button>
                                <span className="text-xl font-bold text-black min-w-[3rem] text-center">
                                  {loanRepaymentByYearData.ageRange.start}
                                </span>
                                <button
                                  onClick={() => setLoanRepaymentByYearData(prev => ({
                                    ...prev,
                                    ageRange: { ...prev.ageRange, start: Math.min(100, prev.ageRange.start + 1) }
                                  }))}
                                  className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white"
                                >
                                  ▶
                                </button>
                              </div>
                            </div>
                            <div>
                              <label className="block text-sm font-bold text-black mb-2">End Age</label>
                              <div className="flex items-center space-x-3 bg-white border-2 border-gray-300 rounded-lg p-3">
                                <button
                                  onClick={() => setLoanRepaymentByYearData(prev => ({
                                    ...prev,
                                    ageRange: { ...prev.ageRange, end: Math.max(prev.ageRange.start, prev.ageRange.end - 1) }
                                  }))}
                                  className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white"
                                >
                                  ◀
                                </button>
                                <span className="text-xl font-bold text-black min-w-[3rem] text-center">
                                  {loanRepaymentByYearData.ageRange.end}
                                </span>
                                <button
                                  onClick={() => setLoanRepaymentByYearData(prev => ({
                                    ...prev,
                                    ageRange: { ...prev.ageRange, end: Math.min(100, prev.ageRange.end + 1) }
                                  }))}
                                  className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white"
                                >
                                  ▶
                                </button>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}

                      {/* Policy Year Range Toggle Bars */}
                      {loanRepaymentByYearData.selectedTypes.policyYear && (
                        <div className="space-y-4">
                          <div className="grid grid-cols-2 gap-6">
                            <div>
                              <label className="block text-sm font-bold text-black mb-2">Start Policy Year</label>
                              <div className="flex items-center space-x-3 bg-white border-2 border-gray-300 rounded-lg p-3">
                                <button
                                  onClick={() => setLoanRepaymentByYearData(prev => ({
                                    ...prev,
                                    policyYearRange: { ...prev.policyYearRange, start: Math.max(calculateCurrentPolicyYear(), prev.policyYearRange.start - 1) }
                                  }))}
                                  className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white"
                                  disabled={loanRepaymentByYearData.policyYearRange.start <= calculateCurrentPolicyYear()}
                                >
                                  ◀
                                </button>
                                <span className="text-xl font-bold text-black min-w-[3rem] text-center">
                                  {loanRepaymentByYearData.policyYearRange.start}
                                </span>
                                <button
                                  onClick={() => setLoanRepaymentByYearData(prev => ({
                                    ...prev,
                                    policyYearRange: { ...prev.policyYearRange, start: Math.min(100, prev.policyYearRange.start + 1) }
                                  }))}
                                  className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white"
                                >
                                  ▶
                                </button>
                              </div>
                            </div>
                            <div>
                              <label className="block text-sm font-bold text-black mb-2">End Policy Year</label>
                              <div className="flex items-center space-x-3 bg-white border-2 border-gray-300 rounded-lg p-3">
                                <button
                                  onClick={() => setLoanRepaymentByYearData(prev => ({
                                    ...prev,
                                    policyYearRange: { ...prev.policyYearRange, end: Math.max(prev.policyYearRange.start, prev.policyYearRange.end - 1) }
                                  }))}
                                  className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white"
                                >
                                  ◀
                                </button>
                                <span className="text-xl font-bold text-black min-w-[3rem] text-center">
                                  {loanRepaymentByYearData.policyYearRange.end}
                                </span>
                                <button
                                  onClick={() => setLoanRepaymentByYearData(prev => ({
                                    ...prev,
                                    policyYearRange: { ...prev.policyYearRange, end: Math.min(100, prev.policyYearRange.end + 1) }
                                  }))}
                                  className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white"
                                >
                                  ▶
                                </button>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}

                      {/* Calendar Year Range Toggle Bars */}
                      {loanRepaymentByYearData.selectedTypes.calendarYear && (
                        <div className="space-y-4">
                          <div className="grid grid-cols-2 gap-6">
                            <div>
                              <label className="block text-sm font-bold text-black mb-2">Start Calendar Year</label>
                              <div className="flex items-center space-x-3 bg-white border-2 border-gray-300 rounded-lg p-3">
                                <button
                                  onClick={() => setLoanRepaymentByYearData(prev => ({
                                    ...prev,
                                    calendarYearRange: { ...prev.calendarYearRange, start: Math.max(getCurrentYear(), prev.calendarYearRange.start - 1) }
                                  }))}
                                  className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white"
                                  disabled={loanRepaymentByYearData.calendarYearRange.start <= getCurrentYear()}
                                >
                                  ◀
                                </button>
                                <span className="text-xl font-bold text-black min-w-[3rem] text-center">
                                  {loanRepaymentByYearData.calendarYearRange.start}
                                </span>
                                <button
                                  onClick={() => setLoanRepaymentByYearData(prev => ({
                                    ...prev,
                                    calendarYearRange: { ...prev.calendarYearRange, start: Math.min(2100, prev.calendarYearRange.start + 1) }
                                  }))}
                                  className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white"
                                >
                                  ▶
                                </button>
                              </div>
                            </div>
                            <div>
                              <label className="block text-sm font-bold text-black mb-2">End Calendar Year</label>
                              <div className="flex items-center space-x-3 bg-white border-2 border-gray-300 rounded-lg p-3">
                                <button
                                  onClick={() => setLoanRepaymentByYearData(prev => ({
                                    ...prev,
                                    calendarYearRange: { ...prev.calendarYearRange, end: Math.max(prev.calendarYearRange.start, prev.calendarYearRange.end - 1) }
                                  }))}
                                  className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white"
                                >
                                  ◀
                                </button>
                                <span className="text-xl font-bold text-black min-w-[3rem] text-center">
                                  {loanRepaymentByYearData.calendarYearRange.end}
                                </span>
                                <button
                                  onClick={() => setLoanRepaymentByYearData(prev => ({
                                    ...prev,
                                    calendarYearRange: { ...prev.calendarYearRange, end: Math.min(2100, prev.calendarYearRange.end + 1) }
                                  }))}
                                  className="w-8 h-8 border-2 border-gray-300 text-gray-700 rounded flex items-center justify-center hover:border-gray-400 hover:text-gray-900 bg-white"
                                >
                                  ▶
                                </button>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>

                    {/* Buttons Row and Data Table - only show when a type is selected */}
                    {(loanRepaymentByYearData.selectedTypes.age || 
                      loanRepaymentByYearData.selectedTypes.policyYear || 
                      loanRepaymentByYearData.selectedTypes.calendarYear) && (
                      <>
                        <div className="flex justify-between items-center mt-6 mb-4">
                          <button className="bg-blue-500 text-white px-4 py-2 rounded-lg font-semibold hover:bg-gray-600 transition-colors">
                            View Year by Year Details
                          </button>
                          <button
                            onClick={() => setLoanRepaymentByYearData(prev => ({ ...prev, isEditing: !prev.isEditing }))}
                            className="bg-blue-500 text-white px-4 py-2 rounded-lg font-semibold hover:bg-blue-600 transition-colors"
                          >
                            {loanRepaymentByYearData.isEditing ? 'Lock Schedule' : 'Modify Schedule'}
                          </button>
                        </div>

                        {/* Data Table */}
                        <div className="mt-4">
                          <div className="overflow-x-auto">
                            <table className="w-full border-collapse border border-gray-300">
                              <thead>
                                <tr className="bg-gray-100">
                                  <th className="border border-gray-300 px-4 py-2 text-left font-bold text-black">Age</th>
                                  <th className="border border-gray-300 px-4 py-2 text-left font-bold text-black">Policy Year</th>
                                  <th className="border border-gray-300 px-4 py-2 text-left font-bold text-black">Calendar Year</th>
                                  <th className="border border-gray-300 px-4 py-2 text-left font-bold text-black">Repayment Amount</th>
                                </tr>
                              </thead>
                              <tbody>
                                {loanRepaymentByYearData.tableData.length === 0 ? (
                                  <tr>
                                    <td
                                      colSpan={4}
                                      className="border border-gray-300 px-4 py-2 text-center text-gray-500"
                                    >
                                      Select year range to populate table
                                    </td>
                                  </tr>
                                ) : (
                                  loanRepaymentByYearData.tableData.map((row, index) => (
                                    <tr key={index}>
                                      <td className="border border-gray-300 px-4 py-2 text-black font-semibold">{row.age}</td>
                                      <td className="border border-gray-300 px-4 py-2 text-black font-semibold">{row.policyYear}</td>
                                      <td className="border border-gray-300 px-4 py-2 text-black font-semibold">{row.calendarYear}</td>
                                      <td className="border border-gray-300 px-4 py-2">
                                        <input
                                          type="number"
                                          value={row.repaymentAmount || ''}
                                          readOnly={!loanRepaymentByYearData.isEditing}
                                          onChange={(e) => {
                                            if (loanRepaymentByYearData.isEditing) {
                                              const newTableData = [...loanRepaymentByYearData.tableData];
                                              // Enhanced number parsing for Safari compatibility using safe parsing
                                              const inputValue = e.target.value;
                                              let numericValue = 0;

                                              if (inputValue && inputValue.trim() !== '') {
                                                const parsed = safeNumberParse(inputValue);
                                                numericValue = parsed !== null ? Math.round(Math.abs(parsed)) : 0;
                                              }

                                              newTableData[index].repaymentAmount = numericValue;
                                              setLoanRepaymentByYearData(prev => ({ ...prev, tableData: newTableData }));
                                            }
                                          }}
                                          onBlur={(e) => {
                                            // Ensure the value is properly formatted on blur using safe parsing
                                            if (loanRepaymentByYearData.isEditing) {
                                              const inputValue = e.target.value;
                                              if (inputValue && inputValue.trim() !== '') {
                                                const parsed = safeNumberParse(inputValue);
                                                const numericValue = parsed !== null ? Math.round(Math.abs(parsed)) : 0;
                                                e.target.value = numericValue.toString();
                                              }
                                            }
                                          }}
                                          className={`w-full p-2 border rounded text-black font-semibold ${
                                            loanRepaymentByYearData.isEditing
                                              ? 'border-blue-300 bg-white'
                                              : 'border-gray-300 bg-gray-100'
                                          }`}
                                          step="1000"
                                          min="0"
                                          placeholder="0"
                                        />
                                      </td>
                                    </tr>
                                  ))
                                )}
                              </tbody>
                            </table>
                          </div>
                        </div>
                      </>
                    )}
                  </div>
                )}
              </div>

              {/* Option 3: Lump Sum (One-time premium) now */}
              <div className="space-y-2">
                <label className="flex items-center text-lg font-semibold text-black mb-2">
                  <input
                    type="radio"
                    name="repaymentType"
                    value="lump-sum"
                    checked={repaymentType === 'lump-sum'}
                    onClick={() => handleOptionSelect('lump-sum')}
                    readOnly
                    className="mr-2"
                  />
                  Lump Sum (One-time premium) now
                </label>
                {repaymentType === 'lump-sum' && (
                  <div className="grid grid-cols-2 gap-4 mt-2 ml-6">
                    <div>
                      <Input
                        type="number"
                        value={lumpSumAmount}
                        onChange={(e) => {
                          // Enhanced number input handling for Safari compatibility
                          const inputValue = e.target.value;
                          // Allow only numbers and decimal points
                          const sanitized = inputValue.replace(/[^0-9.]/g, '');
                          setLumpSumAmount(sanitized);
                        }}
                        onBlur={(e) => {
                          // Format the value on blur for consistency using safe parsing
                          const inputValue = e.target.value;
                          if (inputValue && inputValue.trim() !== '') {
                            const parsed = safeNumberParse(inputValue);
                            if (parsed !== null && parsed >= 0) {
                              setLumpSumAmount(parsed.toString());
                            } else {
                              setLumpSumAmount('');
                            }
                          }
                        }}
                        placeholder="Enter lump sum amount (e.g., 25000)"
                        className="w-full p-3 border-2 border-gray-300 rounded-lg font-semibold text-black bg-blue-50"
                        min="0"
                        step="1000"
                      />
                    </div>
                  </div>
                )}
              </div>

              {/* Option 4: Interest only Payment */}
              <label className="flex items-center text-lg font-semibold text-black mb-2">
                <input
                  type="radio"
                  name="repaymentType"
                  value="interest-only"
                  checked={repaymentType === 'interest-only'}
                  onClick={() => handleOptionSelect('interest-only')}
                  readOnly
                  className="mr-2"
                />
                Interest only Payment
              </label>
            </div>
          </Card>
          {/* Action Buttons */}
          <div className="flex flex-wrap gap-4 justify-center">
            <Button
              onClick={saveScenario}
              variant="primary"
              className="flex items-center space-x-2 bg-indigo-600 hover:bg-indigo-700 text-white shadow-lg border-none"
              disabled={isSaving}
            >
              <Save className="w-4 h-4" />
              <span>{isSaving ? 'Saving...' : 'Save Loan Repayment Illustration'}</span>
            </Button>
            <Button
              onClick={handleResetScenarios}
              variant="primary"
              className="flex items-center space-x-2 bg-indigo-600 hover:bg-indigo-700 text-white shadow-lg border-none"
            >
              <RotateCcw className="w-4 h-4" />
              <span>Reset Scenarios</span>
            </Button>
          </div>
        </>
      )}
    </div>
  );
};

export default LoanRepaymentPage;