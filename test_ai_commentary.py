#!/usr/bin/env python3
"""
Test script to verify the AI Commentary integration
"""
import requests
import json
import time

def test_llm_commentary_backend():
    """Test the LLM commentary backend service with GET method"""
    print("🔍 Testing LLM Commentary Backend (GET method)...")

    # Sample scenario data for testing
    test_data = [
        {
            "policyYear": 2025,
            "age": 40,
            "plannedPremium": "$10,000",
            "netOutlay": "$5,000",
            "netSurrenderValue": "$50,000",
            "netDeathBenefit": "$250,000"
        },
        {
            "policyYear": 2026,
            "age": 41,
            "plannedPremium": "$10,000",
            "netOutlay": "$15,000",
            "netSurrenderValue": "$55,000",
            "netDeathBenefit": "$255,000"
        },
        {
            "policyYear": 2027,
            "age": 42,
            "plannedPremium": "$10,000",
            "netOutlay": "$25,000",
            "netSurrenderValue": "$62,000",
            "netDeathBenefit": "$260,000"
        }
    ]

    try:
        print("📤 Sending GET request to LLM commentary service...")
        print(f"📋 Test data: {json.dumps(test_data, indent=2)}")

        # Prepare GET request parameters
        params = {
            'policyNumber': 'TEST-001',
            'customerName': 'John Doe',
            'policyType': 'Whole Life',
            'scenarioName': 'Test Scenario',
            'scenarioCategory': 'as-is',
            'scenarioData': json.dumps(test_data)
        }

        response = requests.get(
            "http://localhost:5000/api/commentary",
            params=params,
            timeout=30
        )

        print(f"📡 Response status: {response.status_code}")

        if response.status_code == 200:
            data = response.json()
            print("✅ Success! LLM Commentary Backend is working")

            # Check if we got real data or error
            if data.get("response") and data["response"].get("formatted"):
                print("\n====== RAW RESPONSE ======")
                print(data.get("response", {}).get("raw", "No raw response"))
                print("\n====== FORMATTED RESPONSE ======")
                print(data.get("response", {}).get("formatted", "No formatted response"))
                return True
            else:
                print("⚠️ Backend returned empty response - AI service may not be configured")
                return False
        else:
            print(f"❌ Error: {response.status_code}")
            print(f"Response: {response.text}")
            return False

    except requests.exceptions.ConnectionError:
        print("❌ Connection Error: LLM Commentary service is not running")
        print("💡 Please start the LLM service with: cd llm_commentry && python run.py")
        return False
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False

def test_frontend_integration():
    """Test if the frontend can reach the LLM service"""
    print("\n🔍 Testing Frontend Integration...")
    
    # Check if the frontend development server is running
    try:
        response = requests.get("http://localhost:5173", timeout=5)
        if response.status_code == 200:
            print("✅ Frontend development server is running")
        else:
            print("⚠️ Frontend server responded but may have issues")
    except requests.exceptions.ConnectionError:
        print("❌ Frontend development server is not running")
        print("💡 Please start the frontend with: npm run dev")
        return False
    except Exception as e:
        print(f"❌ Error checking frontend: {str(e)}")
        return False
    
    return True

def main():
    """Main test function"""
    print("🚀 AI Commentary Integration Test")
    print("=" * 50)
    
    # Test backend
    backend_ok = test_llm_commentary_backend()
    
    # Test frontend
    frontend_ok = test_frontend_integration()
    
    print("\n" + "=" * 50)
    print("📊 TEST RESULTS:")
    print(f"Backend LLM Service: {'✅ PASS' if backend_ok else '❌ FAIL'}")
    print(f"Frontend Service: {'✅ PASS' if frontend_ok else '❌ FAIL'}")
    
    if backend_ok and frontend_ok:
        print("\n🎉 All tests passed! AI Commentary integration is ready.")
        print("\n📋 Next steps:")
        print("1. Open the frontend application")
        print("2. Select a policy and scenario")
        print("3. Click the 'AI Commentary' button")
        print("4. Verify that real AI commentary is displayed")
    else:
        print("\n⚠️ Some tests failed. Please check the services and try again.")
        
        if not backend_ok:
            print("\n🔧 To start the LLM Commentary backend:")
            print("cd llm_commentry")
            print("pip install -r requirement.txt")
            print("python run.py")
            
        if not frontend_ok:
            print("\n🔧 To start the frontend:")
            print("npm install")
            print("npm run dev")

if __name__ == "__main__":
    main()
